"use client";

import React from "react";
import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON>, Heart } from "lucide-react";

const HomePage = () => {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section - Mobile First */}
      <section className="relative w-full">
        <div className="relative aspect-[4/3] sm:aspect-[16/9] lg:aspect-[21/9]">
          <Image
            src="/home-page/hero-cover.png"
            alt="Pets - <PERSON>, <PERSON>, and Rabbit"
            fill
            priority
            sizes="100vw"
            className="object-cover"
          />
        </div>
      </section>

      {/* AI Doctor Section - Mobile First */}
      <section className="py-8 sm:py-12 lg:py-16 px-4">
        <div className="max-w-7xl mx-auto">
          <h2 className="text-xl sm:text-2xl lg:text-3xl font-bold text-center mb-6 sm:mb-8 lg:mb-12 text-gray-800 px-2">
            <PERSON><PERSON><PERSON> sử dụng AI doctor hiệu quả
          </h2>
          <div className="flex flex-col lg:grid lg:grid-cols-2 gap-6 sm:gap-8 lg:gap-12 items-center">
            {/* Video Section */}
            <div className="relative w-full order-2 lg:order-1">
              <div className="bg-gray-800 rounded-lg aspect-video overflow-hidden shadow-lg">
                <iframe
                  width="100%"
                  height="100%"
                  src="https://www.youtube.com/embed/k5tIGl7wqCg"
                  title="YouTube video player"
                  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                  allowFullScreen
                  className="rounded-lg border-0"
                ></iframe>
              </div>
            </div>

            {/* Features List */}
            <div className="space-y-4 sm:space-y-6 w-full order-1 lg:order-2">
              {[
                "Lorem ipsum dolor sit amet, consectetur adipiscing elit.",
                "Lorem ipsum dolor",
                "Lorem ipsum dolor",
                "Lorem ipsum dolor",
              ].map((feature, index) => (
                <div key={index} className="flex items-start space-x-3 sm:space-x-4">
                  <div className="bg-blue-500 text-white rounded-full w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center flex-shrink-0 mt-1 text-sm sm:text-base font-semibold">
                    {index + 1}
                  </div>
                  <p className="text-gray-700 text-sm sm:text-base lg:text-lg leading-relaxed">{feature}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Categories Section - Mobile First */}
      <section className="py-3 lg:py-12 px-4">
        <div className="max-w-7xl w-full mx-auto">
          {/* Mobile: Scrollable horizontal list */}
          <div className="flex gap-3 sm:gap-4 lg:gap-6 overflow-x-auto pb-2 sm:pb-4 lg:justify-center scrollbar-hide">
            {[
              { name: "Food", icon: "/home-page/catagories/food-icon.png" },
              { name: "Accessories", icon: "/home-page/catagories/toy-icon.png" },
              { name: "Pet shop", icon: "/home-page/catagories/card-icon.png" },
              { name: "Treat/Complementary", icon: "/home-page/catagories/treat-icon.png" },
            ].map((category) => (
              <Button
                key={category.name}
                variant="outline"
                className="flex-shrink-0 h-12 sm:h-14 lg:h-16 px-3 sm:px-6 lg:px-8 rounded-full border-2 border-pawhub-primary text-green-700 hover:bg-yellow-50 text-xs sm:text-sm lg:text-base whitespace-nowrap"
              >
                <Image
                  src={category.icon}
                  alt={category.name}
                  width={32}
                  height={32}
                  className="mr-2 sm:mr-3 w-6 h-6 sm:w-8 sm:h-8 lg:w-10 lg:h-10"
                />
                <span className="hidden sm:inline">{category.name}</span>
                <span className="sm:hidden">{category.name.split('/')[0]}</span>
              </Button>
            ))}
          </div>
        </div>
      </section>

      {/* Hot Deals Section - Mobile First */}
      <section className="py-8 sm:py-12 lg:py-16 px-4 bg-white">
        <div className="max-w-7xl mx-auto">
          {/* Header - Mobile optimized */}
          <div className="flex flex-col sm:flex-row items-center justify-center mb-6 sm:mb-8 lg:mb-12 gap-2 sm:gap-4">
            <Image
              src="/home-page/hot-deal-icon.png"
              alt="Hot deal"
              width={80}
              height={104}
              className="sm:w-[90px] sm:h-[117px] lg:w-[110px] lg:h-[144px]"
            />
            <h2 className="text-lg sm:text-2xl lg:text-3xl font-bold text-gray-800 text-center sm:text-left">
              Hot deal hôm nay
            </h2>
          </div>

          {/* Products Grid - Mobile First */}
          <div className="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 lg:gap-6">
            {Array.from({ length: 4 }).map((_, index) => (
              <Card
                key={index}
                className="group hover:shadow-lg transition-shadow"
              >
                <CardContent className="p-2 sm:p-3 lg:p-4">
                  <div className="relative mb-2 sm:mb-3 lg:mb-4">
                    <div className="bg-gray-200 rounded-lg aspect-square flex items-center justify-center">
                      <span className="text-2xl sm:text-3xl lg:text-4xl">
                        {["🦴", "💋", "🥫", "🧸"][index]}
                      </span>
                    </div>
                    <Badge className="absolute top-1 sm:top-2 left-1 sm:left-2 bg-yellow-400 text-black text-xs">
                      $
                    </Badge>
                    <button className="absolute top-1 sm:top-2 right-1 sm:right-2 p-1 rounded-full bg-white shadow-md">
                      <Heart className="w-3 h-3 sm:w-4 sm:h-4 text-gray-400" />
                    </button>
                  </div>
                  <div className="space-y-1 sm:space-y-2">
                    <div className="flex text-yellow-400 text-xs sm:text-sm">
                      {Array.from({ length: 5 }).map((_, i) => (
                        <Star key={i} className="w-3 h-3 sm:w-4 sm:h-4 fill-current" />
                      ))}
                    </div>
                    <h3 className="font-semibold text-gray-800 text-xs sm:text-sm lg:text-base line-clamp-2">
                      Angel's Bowl/Rectangle Smart & Stay Active Gum
                    </h3>
                    <div className="flex items-center space-x-1 sm:space-x-2">
                      <span className="text-red-500 line-through text-xs sm:text-sm">
                        25.000₫
                      </span>
                      <span className="font-bold text-sm sm:text-base lg:text-lg">23.000₫</span>
                    </div>
                    <Button className="w-full bg-yellow-400 hover:bg-yellow-500 text-black text-xs sm:text-sm py-1 sm:py-2">
                      Add to cart
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Services Section - Mobile First */}
      <section className="py-8 sm:py-12 lg:py-16 px-4">
        <div className="max-w-7xl mx-auto">
          <h2 className="text-lg sm:text-2xl lg:text-3xl font-bold text-center mb-6 sm:mb-8 lg:mb-12 text-gray-800 px-2">
            Dịch vụ thú cưng gần bạn
          </h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6 lg:gap-8">
            {[
              { name: "Veterinary", image: "/home-page/services/Item1.png" },
              { name: "Grooming", image: "/home-page/services/Item-2.png" },
              { name: "Hotel", image: "/home-page/services/Item-3.png" },
              { name: "Pet shop", image: "/home-page/services/Item-4.png" },
            ].map((service) => (
              <div key={service.name} className="w-full">
                <Image
                  src={service.image}
                  alt={service.name}
                  width={633}
                  height={220}
                  className="w-full h-auto cursor-pointer hover:opacity-80 transition-opacity"
                />
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Adoption Section - Mobile First */}
      <section className="py-8 sm:py-12 lg:py-16 px-4 bg-white">
        <div className="max-w-7xl mx-auto">
          <h2 className="text-lg sm:text-2xl lg:text-3xl font-bold text-center mb-6 sm:mb-8 lg:mb-12 text-gray-800 px-2">
            Ready for adoption
          </h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8">
            {[
              { name: "Zong", age: "1 tuổi", gender: "Đực", type: "Chó" },
              { name: "Siêng", age: "6 tháng", gender: "Cái", type: "Mèo" },
              { name: "Ba", age: "1 tuổi", gender: "Đực", type: "Chó" },
            ].map((pet, index) => (
              <Card
                key={index}
                className="group hover:shadow-lg transition-shadow"
              >
                <CardContent className="p-0">
                  <div className="relative">
                    <div className="bg-gray-200 rounded-t-lg aspect-square flex items-center justify-center">
                      <span className="text-5xl sm:text-6xl lg:text-8xl">
                        {["🐕", "🐱", "🐕"][index]}
                      </span>
                    </div>
                    <button className="absolute top-2 sm:top-3 lg:top-4 right-2 sm:right-3 lg:right-4 p-1 sm:p-1.5 lg:p-2 rounded-full bg-white shadow-md">
                      <Heart className="w-4 h-4 sm:w-4 sm:h-4 lg:w-5 lg:h-5 text-gray-400" />
                    </button>
                  </div>
                  <div className="p-3 sm:p-4">
                    <h3 className="font-bold text-lg sm:text-xl mb-1 sm:mb-2">{pet.name}</h3>
                    <div className="text-gray-600 space-y-1 mb-3 sm:mb-4">
                      <p className="text-sm sm:text-base">
                        {pet.type} • {pet.age} • {pet.gender}
                      </p>
                    </div>
                    <Button className="w-full bg-yellow-400 hover:bg-yellow-500 text-black text-sm sm:text-base py-2">
                      Nhận nuôi
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* News Section - Mobile First */}
      <section className="py-8 sm:py-12 lg:py-16 px-4">
        <div className="max-w-7xl mx-auto">
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-6 sm:mb-8 lg:mb-12 gap-3 sm:gap-4">
            <h2 className="text-lg sm:text-2xl lg:text-3xl font-bold text-gray-800 px-2 sm:px-0">
              Tin tức mới nhất từ Pawhub
            </h2>
            <Button variant="outline" className="text-sm sm:text-base px-3 sm:px-4 py-2 self-start sm:self-auto">
              <span className="hidden sm:inline">Xem thêm →</span>
              <span className="sm:hidden">Xem thêm</span>
            </Button>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8">
            {Array.from({ length: 3 }).map((_, index) => (
              <Card
                key={index}
                className="group hover:shadow-lg transition-shadow"
              >
                <CardContent className="p-0">
                  <div className="bg-gray-200 rounded-t-lg aspect-video flex items-center justify-center">
                    <span className="text-2xl sm:text-3xl lg:text-4xl">📰</span>
                  </div>
                  <div className="p-3 sm:p-4 lg:p-6">
                    <h3 className="font-bold text-base sm:text-lg mb-1 sm:mb-2">
                      Pellentesque cursus
                    </h3>
                    <p className="text-gray-600 text-xs sm:text-sm mb-2 sm:mb-4">
                      Aliquam Ut This Ligature • 6 tháng 9, 2024
                    </p>
                    <p className="text-gray-700 text-sm sm:text-base line-clamp-3">
                      Mauris mattis dui ante, at vehicula massa bibendum et.
                      Pellentesque cursus imperdiet lorem, non lacinia nunc
                      rhoncus vitae.
                    </p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
};

export default HomePage;
